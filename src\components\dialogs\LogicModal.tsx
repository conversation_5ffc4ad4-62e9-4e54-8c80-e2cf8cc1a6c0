import { fredoka } from "@/app/fonts";
import { ElementLogicData, FormElement, LogicModalProps } from "@/components/types";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Footer, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { closeLogicModal } from "@/lib/redux/slices/dialogSlice";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { elementLogicSchema } from "@/schemas/logic/logic";
import { generateId } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useFieldArray, useForm } from "react-hook-form";
import { useEffect } from "react";
import Image from "next/image";
import plusIcon from "@/assets/icons/plus-green-outline.svg";

type LogicModalConfig = {
  operatorOptions: string[];
  getParentAnswerOptions: (parentQuestionId: string) => { value: string; label: string }[];
};

const LogicModal = ({ component, screenId, sectionId }: LogicModalProps) => {
  const elementName = component.replace("Properties", "").replace("Field", "") || "";
  console.log(elementName, screenId, sectionId);
  const dispatch = useAppDispatch();
  const isLogicModalOpen = useAppSelector(state => state.dialog.isLogicModalOpen);
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;

  const formScreens = useAppSelector(state => state.form.formScreens);

  const allQuestions = formScreens
    .flatMap(screen => screen.sections)
    .flatMap(section => section.elements)
    .map(element => ({ id: element.id, label: element.label }))
    .filter(element => element.id !== selectedFormBuilderItem.id);

  // Configuration for different field types
  const getLogicModalConfig = (component: string): LogicModalConfig => {
    const baseConfig = {
      operatorOptions: ["is", "is not"],
      getParentAnswerOptions: (parentQuestionId: string) => {
        // Default implementation for choice-based fields
        const parentElement = formScreens
          .flatMap(screen => screen.sections)
          .flatMap(section => section.elements)
          .find(element => element.id === parentQuestionId);

        if (parentElement?.options) {
          return parentElement.options.map((option: string) => ({
            value: option,
            label: option,
          }));
        }
        return [];
      },
    };

    switch (component) {
      case "NumberProperties":
        return {
          operatorOptions: [
            "is equal to",
            "is not equal to",
            "is lower than",
            "is greater than",
            "is lower than or equal to",
            "is greater than or equal to",
          ],
          getParentAnswerOptions: (parentQuestionId: string) => {
            // For number fields, parent answers are free text input
            return [{ value: "custom", label: "Enter value" }];
          },
        };

      case "ShortAnswerProperties":
      case "ParagraphProperties":
        return {
          operatorOptions: ["is", "is not", "contains", "does not contain", "starts with", "ends with"],
          getParentAnswerOptions: (parentQuestionId: string) => {
            // For text fields, parent answers are free text input
            return [{ value: "custom", label: "Enter value" }];
          },
        };

      case "DateProperties":
        return {
          operatorOptions: ["is", "is not", "before", "after", "on or before", "on or after"],
          getParentAnswerOptions: (parentQuestionId: string) => {
            // For date fields, parent answers are date input
            return [{ value: "custom", label: "Select date" }];
          },
        };

      case "TimeProperties":
        return {
          operatorOptions: ["is", "is not", "before", "after"],
          getParentAnswerOptions: (parentQuestionId: string) => {
            // For time fields, parent answers are time input
            return [{ value: "custom", label: "Select time" }];
          },
        };

      case "PhoneNumberProperties":
        return {
          operatorOptions: ["is", "is not", "contains", "does not contain"],
          getParentAnswerOptions: (parentQuestionId: string) => {
            // For phone number fields, parent answers are free text input
            return [{ value: "custom", label: "Enter phone number" }];
          },
        };

      case "RatingProperties":
        return {
          operatorOptions: [
            "is equal to",
            "is not equal to",
            "is lower than",
            "is greater than",
            "is lower than or equal to",
            "is greater than or equal to",
          ],
          getParentAnswerOptions: (parentQuestionId: string) => {
            // For rating fields, parent answers are numeric input
            return [{ value: "custom", label: "Enter rating value" }];
          },
        };

      case "SingleChoiceProperties":
      case "MultipleChoiceProperties":
      case "DropdownProperties":
        return baseConfig; // These use the default choice-based logic

      default:
        return baseConfig;
    }
  };

  const logicConfig = getLogicModalConfig(component);
  const logicOptions = ["and", "or"];

  const handleOpenChange = (open: boolean): void => {
    if (!open) {
      dispatch(closeLogicModal());
    }
  };

  const handleAddLogic = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    // appendLogic({
    //   id: `logic_${generateId()}`,
    //   if: [
    //     {
    //       id: `condition_${generateId()}`,
    //       parentQuestion: "",
    //       parentAnswer: "",
    //       operator: "",
    //       logic: null,
    //     },
    //   ],
    //   then: "",
    // });
  };

  const getDefaultValues = () => {
    // If the selected element already has logic, use it; otherwise create a new one
    if (selectedFormBuilderItem?.logics && selectedFormBuilderItem.logics.length > 0) {
      return { logics: selectedFormBuilderItem.logics };
    }

    return {
      logics: [
        {
          id: `logic_${generateId()}`,
          if: [
            {
              id: `condition_${generateId()}`,
              parentQuestion: "",
              parentAnswer: "",
              operator: "",
              logic: null,
            },
          ],
          then: "show",
        },
      ],
    };
  };

  const form = useForm<ElementLogicData>({
    resolver: zodResolver(elementLogicSchema),
    mode: "onChange",
    defaultValues: getDefaultValues(),
  });

  const { control, reset } = form;

  // Reset form when modal opens or selected item changes
  useEffect(() => {
    if (isLogicModalOpen) {
      reset(getDefaultValues());
    }
  }, [isLogicModalOpen, selectedFormBuilderItem?.id]);

  const {
    fields: logicFields,
    append: appendLogic,
    remove: removeLogic,
  } = useFieldArray({
    control,
    name: "logics",
  });

  const applyChanges = (data: ElementLogicData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Dialog open={isLogicModalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="w-[60rem] px-8">
        <DialogHeader>
          <DialogTitle className={`${fredoka.className} text-lg font-semibold`}>Logic</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onChange={form.handleSubmit(applyChanges)} className="space-y-4">
            {logicFields.map((logicField, logicIndex) => {
              const {
                fields: conditionFields,
                append: appendCondition,
                remove: removeCondition,
              } = useFieldArray({
                control: form.control,
                name: `logics.${logicIndex}.if`,
              });

              const handleAddCondition = (e: React.MouseEvent<HTMLButtonElement>) => {
                e.preventDefault();
                appendCondition({
                  id: `condition_${generateId()}`,
                  parentQuestion: "",
                  parentAnswer: "",
                  operator: "",
                  logic: "and",
                });
              };
              return (
                <div key={logicField.id} className="rounded-[10px] border border-[#75748F] p-4">
                  <div className="flex flex-col gap-2">
                    {conditionFields.map((conditionField, conditionIndex) => (
                      <div key={conditionField.id} className="space-y-4">
                        <div className={`w-[10rem] ${conditionField.logic !== null ? "block" : "hidden"}`}>
                          <FormField
                            control={form.control}
                            name={`logics.${logicIndex}.if.${conditionIndex}.logic`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Select onValueChange={field.onChange} value={field.value ?? ""}>
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {logicOptions.map(option => (
                                        <SelectItem key={option} value={option}>
                                          {option}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>
                        <FormField
                          control={form.control}
                          name={`logics.${logicIndex}.if.${conditionIndex}.parentQuestion`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={`${conditionIndex === 0 ? "block" : "hidden"}`}>If</FormLabel>
                              <FormControl>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Parent Question" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {allQuestions.map(question => (
                                      <SelectItem key={question.id} value={question.id}>
                                        {question.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <div className="flex gap-4">
                          <div className="w-[20rem]">
                            <FormField
                              control={form.control}
                              name={`logics.${logicIndex}.if.${conditionIndex}.operator`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Select onValueChange={field.onChange} value={field.value}>
                                      <SelectTrigger>
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {logicConfig.operatorOptions.map((operator: string) => (
                                          <SelectItem key={operator} value={operator}>
                                            {operator}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="w-full">
                            <FormField
                              control={form.control}
                              name={`logics.${logicIndex}.if.${conditionIndex}.parentAnswer`}
                              render={({ field }) => {
                                const selectedParentQuestion = form.watch(`logics.${logicIndex}.if.${conditionIndex}.parentQuestion`);
                                const parentAnswerOptions = logicConfig.getParentAnswerOptions(selectedParentQuestion || "");

                                // For custom input fields (text, number, date, time)
                                if (parentAnswerOptions.length === 1 && parentAnswerOptions[0].value === "custom") {
                                  return (
                                    <FormItem>
                                      <FormControl>
                                        <Input placeholder={parentAnswerOptions[0].label} value={field.value} onChange={field.onChange} />
                                      </FormControl>
                                    </FormItem>
                                  );
                                }

                                // For select options (choice-based fields)
                                return (
                                  <FormItem>
                                    <FormControl>
                                      <Select onValueChange={field.onChange} value={field.value}>
                                        <SelectTrigger>
                                          <SelectValue placeholder="Parent Question Option" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {parentAnswerOptions.map(option => (
                                            <SelectItem key={option.value} value={option.value}>
                                              {option.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </FormControl>
                                  </FormItem>
                                );
                              }}
                            />
                          </div>
                        </div>
                        <div className="flex justify-end">
                          <Button
                            variant="ghost"
                            className={`cursor-pointer items-center gap-1 p-0 font-normal hover:bg-transparent ${conditionIndex === conditionFields.length - 1 ? "flex" : "hidden"}`}
                            onClick={handleAddCondition}
                          >
                            <Image src={plusIcon} alt="Plus Icon" width={14} height={14} />
                            Add Condition
                          </Button>
                        </div>
                      </div>
                    ))}
                    <div className="flex items-center gap-4">
                      <div className="w-[10rem]">
                        <FormField
                          control={form.control}
                          name={`logics.${logicIndex}.then`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Then</FormLabel>
                              <FormControl>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="show">show</SelectItem>
                                    <SelectItem value="hide">hide</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="w-full space-y-2">
                        <FormLabel className="invisible">Dummy</FormLabel>
                        <Input value={selectedFormBuilderItem?.label} readOnly />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </form>
        </Form>
        <div>
          <Button className={`${fredoka.className} font-semibold`} type="button" onClick={handleAddLogic}>
            Add Logic
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LogicModal;
